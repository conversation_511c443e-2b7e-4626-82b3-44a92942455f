import yaml
from pathlib import Path

class Config:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(Config, cls).__new__(cls)
            cls._instance._load_config()
        return cls._instance
    
    def _load_config(self):
        try:
            config_path = Path(__file__).parent.parent / 'config' / 'config.yaml'
            print(f"正在读取配置文件: {config_path}")
            if not config_path.exists():
                print(f"错误: 配置文件不存在: {config_path}")
                raise FileNotFoundError(f"配置文件不存在: {config_path}")
            
            with open(config_path, 'r', encoding='utf-8') as f:
                self._config = yaml.safe_load(f)
            print("配置文件读取成功")
        except Exception as e:
            print(f"配置文件读取错误: {e}")
            import traceback
            traceback.print_exc()
            raise
    
    @property
    def ga_params(self):
        return self._config['ga_params']
    
    @property
    def airport_params(self):
        return self._config['airport_params']
    
    @property
    def aircraft_params(self):
        return self._config['aircraft_params'] 