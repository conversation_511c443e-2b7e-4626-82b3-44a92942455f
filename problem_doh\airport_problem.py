import json
import pickle
import time

import networkx as nx
import numpy as np
from matplotlib import pyplot as plt
import pandas as pd
from networkx.readwrite import json_graph
from tqdm import tqdm

from problem_doh.data_loader import load_airport_data
import math
from utils.config import Config


def all_simple_paths_custom(G, source, target):
    if source not in G or target not in G:
        raise nx.NodeNotFound("源或目标节点不在图中。")

    # 预存每个节点的邻居列表
    neighbors_dict = {n: list(G[n]) for n in G}

    visited = {node: False for node in G}
    visited[source] = True
    path = [source]

    # 栈结构：每个元素为 (current_node, neighbors_list, index)
    stack = [(source, neighbors_dict[source], 0)]

    with tqdm(total=5000000, position=0, desc="计算all_simple_paths") as pbar:
        while stack:
            current_node, children, index = stack[-1]
            if index < len(children):
                neighbor = children[index]
                stack[-1] = (current_node, children, index + 1)

                if neighbor == target:
                    path.append(neighbor)
                    pbar.update(1)
                    yield path.copy()
                    path.pop()
                elif not visited[neighbor]:
                    visited[neighbor] = True
                    path.append(neighbor)
                    stack.append((neighbor, neighbors_dict[neighbor], 0))
            else:
                stack.pop()
                removed = path.pop()
                visited[removed] = False


class AirportSchedulingProblem:
    def __init__(self, data_file):
        try:
            # 新增配置初始化
            self.config = Config()

            # 加载数据
            print("正在加载机场数据...")
            self.G, self.nodes_df, self.edges_df, self.aircraft_df = load_airport_data(data_file)
            print(f"数据加载完成，飞机数量: {len(self.aircraft_df)}")

            # 预计算所有可能的路径及其直弯道划分
            print("预计算航班可能路径...")
            self.possible_paths = {}
            self._precompute_paths()
            print(f"路径计算完成，共 {sum(len(paths) for paths in self.possible_paths.values())} 条可行路径")

        except Exception as e:
            print(f"机场问题初始化错误: {e}")
            import traceback
            traceback.print_exc()
            raise

    def _calculate_angle(self, point1, point2, point3):
        """
        计算向量 (node1-node2) 和 (node3-node2) 之间的锐角
        使用投影坐标系统提高精度（与graph_builder.py保持一致）

        参数:
        - node1: 第一个节点坐标 (longitude, latitude)
        - node2: 中心节点坐标 (longitude, latitude)
        - node3: 第三个节点坐标 (longitude, latitude)

        返回:
        - 角度(度数)
        """
        # 提取坐标 (经度, 纬度)
        lon1, lat1 = self.nodes_df[self.nodes_df['Node ID'] == point1][['X', 'Y']].values[0]
        lon2, lat2 = self.nodes_df[self.nodes_df['Node ID'] == point2][['X', 'Y']].values[0]
        lon3, lat3 = self.nodes_df[self.nodes_df['Node ID'] == point3][['X', 'Y']].values[0]
        # 选择参考点（使用中心点作为参考）
        ref_lat = np.radians(lat2)

        # 将经纬度坐标投影到平面坐标系（米）
        def project_point(lon, lat):
            dlat_m = (lat - lat2) * 111000  # 1度纬度约111km
            dlon_m = (lon - lon2) * 111000 * np.cos(ref_lat)  # 经度随纬度变化
            return (dlon_m, dlat_m)

        # 投影所有点，中心点为原点(0, 0)
        p1_proj = project_point(lon1, lat1)
        p2_proj = (0, 0)  # 中心点投影为原点
        p3_proj = project_point(lon3, lat3)
        # 计算从中心点指向其他两点的向量
        vector_a = np.array([p1_proj[0] - p2_proj[0], p1_proj[1] - p2_proj[1]])
        vector_b = np.array([p3_proj[0] - p2_proj[0], p3_proj[1] - p2_proj[1]])
        # 计算向量的模长
        norm_a = np.linalg.norm(vector_a)
        norm_b = np.linalg.norm(vector_b)
        # 避免除以零
        if norm_a == 0 or norm_b == 0:
            return 90  # 默认返回90度
        # 计算余弦值
        cos_angle = np.dot(vector_a, vector_b) / (norm_a * norm_b)
        # 将余弦值限制在[-1, 1]范围内，避免数值误差
        cos_angle = np.clip(cos_angle, -1.0, 1.0)
        # 计算角度(弧度转度数)
        angle = np.arccos(cos_angle) * (180 / np.pi)
        # 确保角度是锐角
        if angle > 90:
            angle = 180 - angle

        return angle

    def _divide_path_into_segments(self, path):
        """将路径划分为直道和弯道段"""
        if len(path) < 3:
            return [('straight', path, self.G[path[0]][path[1]]['length'])]

        segments = []
        current_type = 'straight breakaway'  # 默认从直道开始
        current_segment = [path[0], path[1]]  # 初始化第一段
        current_length = self.G[path[0]][path[1]]['length']

        # 从第二个节点开始遍历
        for i in range(1, len(path) - 2):
            angle = self._calculate_angle(path[i - 1], path[i], path[i + 1])

            # 判断下一条边是否为弯道
            next_type = 'curve' if angle > 30 else 'straight'

            if next_type == current_type or (current_type == 'straight breakaway' and next_type == 'straight'):
                # 继续当前段
                current_segment.append(path[i + 1])
                current_length += self.G[path[i]][path[i + 1]]['length']
            else:
                # 保存当前段并开始新段
                segments.append((current_type, current_segment, current_length))
                current_type = next_type
                current_segment = [path[i], path[i + 1]]
                current_length = self.G[path[i]][path[i + 1]]['length']

        # 讨论最终一条edge
        if current_type == 'straight':
            current_segment.append(path[-1])
            current_length += self.G[path[-2]][path[-1]]['length']
            segments.append(('straight holding', current_segment, current_length))
        else:
            segments.append((current_type, current_segment, current_length))
            # 单独添加最后一条
            segments.append(('straight holding', [path[-2], path[-1]], self.G[path[-2]][path[-1]]['length']))

        # 修正直道段类型：根据节点规格判断是否为straight breakaway或straight holding
        final_segments = []
        for seg_type, seg_nodes, length in segments:
            if seg_type == 'curve':
                final_segments.append((seg_type, seg_nodes, length))
                continue
            # 获取起点和终点的规格
            start_node = seg_nodes[0]
            end_node = seg_nodes[-1]
            start_spec = self.nodes_df[self.nodes_df['Node ID'] == start_node]['Specification'].values
            end_spec = self.nodes_df[self.nodes_df['Node ID'] == end_node]['Specification'].values
            start_spec_str = start_spec[0] if len(start_spec) > 0 else ""
            end_spec_str = end_spec[0] if len(end_spec) > 0 else ""
            # 根据节点规格判断路段类型
            if start_spec_str in ['runway', 'gate'] and end_spec_str in ['runway', 'gate']:
                # 如果起点和终点都是特殊节点，添加两种路段类型
                final_segments.append(('straight breakaway', seg_nodes, length))
                final_segments.append(('straight holding', seg_nodes, length))
            elif start_spec_str in ['runway', 'gate']:
                final_segments.append(('straight breakaway', seg_nodes, length))
            elif end_spec_str in ['runway', 'gate']:
                final_segments.append(('straight holding', seg_nodes, length))
            else:
                final_segments.append(('straight', seg_nodes, length))

        return final_segments

    def _precompute_paths(self):
        """预计算每个航班的所有可能路径及其直弯道划分"""
        total_flights = len(self.aircraft_df)
        total_paths = 0

        print(f"开始为{total_flights}个航班计算路径...")

        # 使用tqdm创建进度条
        for idx, flight in self.aircraft_df.iterrows():
            # 获取所有可能路径
            paths = list(all_simple_paths_custom(
                self.G,
                flight['Start Node'],
                flight['End Node']
            ))
            # with open(f'paths_{idx}.json', 'w') as f:
            #     json.dump(paths, f)
            # 更新路径总数以便显示进度
            path_count = len(paths)
            total_paths += path_count

            # 过滤出路径长度小于或等于平均值的路径
            path_weights = {}
            for path in paths:
                # 计算路径的长度
                path_weights[tuple(path)] = nx.path_weight(self.G, path, 'length')

            # # 计算路径长度的平均值
            # average_length = np.mean(list(path_weights.values()))
            # # 过滤出路径长度小于或等于平均值的路径
            # paths = [list(path) for path, weight in path_weights.items() if weight <= average_length]

            # 按路径长度排序
            sorted_paths = sorted(path_weights.items(), key=lambda item: item[1])
            # 取最短的前1000条路径
            paths = [list(path) for path, weight in sorted_paths[:1000]]

            # 对每条路径进行直弯道划分
            path_segments = []
            if path_count > 0:
                for path in tqdm(paths, desc=f"处理航班{idx}的路径", position=0):
                    segments = self._divide_path_into_segments(path)
                    path_segments.append({
                        'full_path': path,
                        'segments': segments
                    })

            self.possible_paths[idx] = path_segments

            # 显示当前进度信息
            if idx % 1 == 0 or idx == total_flights - 1:
                print(f"已处理: {idx + 1}/{total_flights} 航班, 当前航班有 {path_count} 条路径")

        print(f"路径计算完成，共处理 {total_flights} 个航班, {total_paths} 条路径")

    def get_possible_paths(self, flight_idx):
        """获取指定航班的所有可能路径（包含直弯道划分）"""
        return self.possible_paths.get(flight_idx, [])

    def _extract_subgraph_segments(self):
        """
        从所有航班的可能路径中提取子图，并获取子图中所有边作为segments
        返回所有可能的segment类型和长度组合
        """
        print("开始提取子图segments...")

        # 收集所有路径中出现的节点
        all_nodes = set()
        for flight_paths in self.possible_paths.values():
            for path_info in flight_paths:
                path = path_info['full_path']
                all_nodes.update(path)

        print(f"子图包含 {len(all_nodes)} 个节点")

        # 创建子图
        subgraph = self.G.subgraph(all_nodes)

        # 获取子图中所有边作为segments
        all_segments = []
        processed_edges = set()  # 避免重复处理同一条边

        for edge in subgraph.edges():
            # 由于是无向图，避免重复处理 (A,B) 和 (B,A)
            edge_key = tuple(sorted(edge))
            if edge_key in processed_edges:
                continue
            processed_edges.add(edge_key)

            start_node, end_node = edge
            edge_length = self.G[start_node][end_node]['length']

            # 获取节点规格
            start_spec = self.nodes_df[self.nodes_df['Node ID'] == start_node]['Specification'].values
            end_spec = self.nodes_df[self.nodes_df['Node ID'] == end_node]['Specification'].values
            start_spec_str = start_spec[0] if len(start_spec) > 0 else ""
            end_spec_str = end_spec[0] if len(end_spec) > 0 else ""

            # 根据节点规格确定segment类型
            segment_types = []

            if start_spec_str in ['runway', 'gate'] and end_spec_str in ['runway', 'gate']:
                # 如果起点和终点都是特殊节点，可以是两种类型
                segment_types.extend(['straight breakaway', 'straight holding'])
            elif start_spec_str in ['runway', 'gate']:
                segment_types.append('straight breakaway')
            elif end_spec_str in ['runway', 'gate']:
                segment_types.append('straight holding')
            else:
                segment_types.append('straight')

            # 添加所有可能的segment类型
            for seg_type in segment_types:
                all_segments.append({
                    'type': seg_type,
                    'length': edge_length,
                    'start_node': start_node,
                    'end_node': end_node
                })

        print(f"从子图中提取了 {len(all_segments)} 个segments")
        return all_segments


# 飞机类别参数
aircraft_parameters = {
    # Learjet 35A
    'light': {
        'weight': 8300,
        'fuel_flow_7': 0.024,
        'fuel_flow_30': 0.067,
        'F0': 2 * 15.6 * 1000,  # 转换为N
        'mu': 0.015  # 滚动阻力系数
    },
    # Airbus A320
    'Medium': {
        'weight': 78000,
        'fuel_flow_7': 0.101,
        'fuel_flow_30': 0.291,
        'F0': 2 * 111.2 * 1000,
        'mu': 0.015  # 滚动阻力系数
    },
    # Airbus A333
    'Heavy': {
        'weight': 230000,
        'fuel_flow_7': 0.228,
        'fuel_flow_30': 0.724,
        'F0': 2 * 287 * 1000,
        'mu': 0.015  # 滚动阻力系数
    }
}

# 路段类别表，不同路段类别的起始、结束和最大速度，速度单位为米每秒（m/s）
segment_type_speeds = {
    'straight breakaway': (0, 5.14, 15.43),  # 5.14 m/s 对应 10 kn，15.43 m/s 对应 30 kn
    'straight holding': (5.14, 0, 15.43),  # 5.14 m/s 对应 10 kn，15.43 m/s 对应 30 kn
    'straight': (5.14, 5.14, 15.43),  # 5.14 m/s 对应 10 kn，15.43 m/s 对应 30 kn
    'turning': (5.14, 5.14, 5.14)  # 5.14 m/s 对应 10 kn
}


# 线性插值计算燃油流量
def interpolate_fuel_flow(eta, fuel_flow_7, fuel_flow_30):
    return fuel_flow_7 + (fuel_flow_30 - fuel_flow_7) * (eta - 0.07) / (0.30 - 0.07)


# 计算目标函数以及各个决策变量
def calculate_objectives(weight_class, segment_type, segment_length, v1):
    # 根据 segment_type 查询表格中 v0 和 v4 的大小
    v0, v4, vmax = segment_type_speeds[segment_type]
    a4 = 0.98  # 预设的减速度
    # 设定加速度和减速度
    if v1 - v0 > 0:
        a1 = 0.98  # 加速
        d1 = (v1 ** 2 - v0 ** 2) / (2 * a1)
        t1 = (v1 - v0) / a1
    else:
        a1 = 0  # 没有加速
        d1 = 0
        t1 = 0

    # 计算其他决策变量的值
    d4 = (v1 ** 2 - v4 ** 2) / (2 * a4)
    d2 = segment_length - d4 - d1

    t2 = d2 / v1
    t4 = (v1 - v4) / a4
    # 总时间
    total_time = t1 + t2 + t4

    # 获取飞机参数
    aircraft = aircraft_parameters[weight_class]
    weight = aircraft['weight']
    F0 = aircraft['F0']
    mu = aircraft['mu']

    # 计算推力系数
    eta_acc = (weight * a1 + mu * weight * 9.81) / F0
    eta_dec = 0.05
    eta_const = eta_acc

    # 计算燃油流量
    fuel_flow_acc = interpolate_fuel_flow(eta_acc, aircraft['fuel_flow_7'], aircraft['fuel_flow_30'])
    fuel_flow_dec = interpolate_fuel_flow(eta_dec, aircraft['fuel_flow_7'], aircraft['fuel_flow_30'])
    fuel_flow_const = fuel_flow_acc

    # 计算燃油消耗
    fuel_acc = fuel_flow_acc * t1
    fuel_const = fuel_flow_const * t2
    fuel_dec = fuel_flow_dec * t4

    # 总燃油消耗
    total_fuel = fuel_acc + fuel_const + fuel_dec

    return total_time, total_fuel, a1, d1, d2, d4


# 结合速度曲线优化的滑行时间和燃油消耗计算
def run_speed_optimization(weight_class, segment_type, segment_length):
    v_turn = 5.14
    v_straight = 15.43
    step = 1
    p = 12
    solutions = []  # 用于存储帕累托前沿的解
    speed_profiles = []  # 存储所有生成的速度曲线
    profile_data = []  # 存储每个速度曲线对应的所有计算参数和效用

    # Step 1-5: 生成速度曲线
    v1 = v_turn
    while v1 <= v_straight:
        speed_profiles.append(v1)
        v1 += step

    # 确保端点值 15.43 被包含到速度曲线中
    if v_straight not in speed_profiles:
        speed_profiles.append(v_straight)

    # Step 6-11: 对每个权重组合计算效用并选择最优解
    for i in range(p):
        w1 = i / p  # 从 0 到 1，生成权重组合
        w2 = 1 - w1

        # Step 8: 计算效用，g1 和 g2 分别是滑行时间和燃油消耗
        utilities = []
        profile_data = []  # 在每次权重组合下重置 profile_data
        for speed in speed_profiles:
            # 计算滑行时间、燃油消耗和其他参数
            g1, g2, a1, d1, d2, d4 = calculate_objectives(weight_class, segment_type, segment_length, speed)

            # 如果 d2 小于 0，则跳过这个 speed
            if d2 < 0:
                continue  # 直接跳过当前速度

            utility = w1 * g1 + w2 * g2  # 基于权重组合计算效用
            utilities.append(utility)

            # 记录当前速度和相关参数
            profile_data.append({
                'speed': speed,
                'g1': g1,  # 滑行时间
                'g2': g2,  # 燃油消耗
                'a1': a1,
                'd1': d1,
                'd2': d2,
                'd4': d4,
                'utility': utility
            })

        # Step 9: 如果 utilities 为空，跳过该循环
        if not utilities:
            continue

        # 选择效用最小的速度曲线
        min_utility_index = np.argmin(utilities)
        best_profile = profile_data[min_utility_index]

        # Step 10: 检查 solutions 中是否已经存在相同的解
        if not any(
                solution['speed'] == best_profile['speed'] and
                solution['g1'] == best_profile['g1'] and
                solution['g2'] == best_profile['g2'] and
                solution['a1'] == best_profile['a1'] and
                solution['d1'] == best_profile['d1'] and
                solution['d2'] == best_profile['d2'] and
                solution['d4'] == best_profile['d4']
                for solution in solutions
        ):
            # 如果没有相同的解，则将该速度曲线及其参数添加到帕累托前沿解集
            solutions.append({
                'speed': best_profile['speed'],
                'g1': best_profile['g1'],  # 滑行时间
                'g2': best_profile['g2'],  # 燃油消耗
                'a1': best_profile['a1'],
                'd1': best_profile['d1'],
                'd2': best_profile['d2'],
                'd4': best_profile['d4']
            })

    # Step 12: 返回帕累托前沿的解集
    return solutions


for data_file in ['doh1']:
    start = time.perf_counter()
    p = AirportSchedulingProblem(data_file)
    end = time.perf_counter()
    print(f"运行时间: {end - start:.6f} 秒")

    # 使用新方法：从子图中提取所有可能的segments
    print("使用子图方法提取segments...")
    subgraph_segments = p._extract_subgraph_segments()

    # 同时保留原有方法的结果用于对比
    print("使用原有方法提取segments...")
    original_segments = []
    for flight_paths in p.possible_paths.values():
        for path in flight_paths:
            for segment_type, nodes, length in path['segments']:
                if segment_type != 'curve':  # 收集所有非curve类型的segment
                    original_segments.append({
                        'type': segment_type,
                        'length': length
                    })

    # 使用子图方法的结果作为主要数据
    straight_segments = subgraph_segments

    # 对比分析新旧方法的差异
    original_df = pd.DataFrame(original_segments)
    subgraph_df = pd.DataFrame(straight_segments)

    print(f"\n=== SEGMENT提取方法对比 ===")
    print(f"原有方法提取的segments数量: {len(original_segments)}")
    print(f"子图方法提取的segments数量: {len(straight_segments)}")
    print(f"新增segments数量: {len(straight_segments) - len(original_segments)}")

    if len(original_segments) > 0:
        print(f"\n原有方法各类型统计：")
        print(original_df.groupby('type')['length'].describe())

    print(f"\n子图方法各类型统计：")
    print(subgraph_df.groupby('type')['length'].describe())

    # 使用子图方法的结果
    segments_df = subgraph_df
    print(f"\n最终使用的segments统计：")
    print(segments_df)

    # 定义数据库结构，存储优化结果
    database = {
        'aircraft_weight_class': [],  # Light, Medium, Heavy
        'segment_type': [],  # Straight, Straight breakaway, Straight holding
        'segment_length': [],  # 路段长度 (米)
        'speed_profile': [],  # 速度曲线，1为最小燃料消耗，其他为不同优化解
        'a1': [],  # 决策变量
        'd1': [],
        'd2': [],
        'd4': [],
        'g1': [],  # 滑行时间
        'g2': []  # 燃油消耗
    }
    # 初始化 processed_pairs 用于存储已处理的边对组合
    processed_pairs = set()

    # 遍历所有边的组合
    print(f"重复行数: {segments_df.duplicated().sum()}")
    segments_df.drop_duplicates(inplace=True, ignore_index=True)

    for row in segments_df.itertuples():
        segment_type, segment_length = row.type, round(row.length, 2)
        # 遍历所有找到的段
        for weight_class in aircraft_parameters.keys():
            pareto_solutions = run_speed_optimization(weight_class, segment_type, segment_length)

            # 批量存储到数据库
            for idx, solution in enumerate(pareto_solutions):
                database['aircraft_weight_class'].append(weight_class)
                database['segment_type'].append(segment_type)
                database['segment_length'].append(segment_length)
                database['speed_profile'].append(idx + 1)  # 速度曲线编号
                database['a1'].append(solution['a1'])
                database['d1'].append(solution['d1'])
                database['d2'].append(solution['d2'])
                database['d4'].append(solution['d4'])
                database['g1'].append(solution['g1'])  # 滑行时间
                database['g2'].append(solution['g2'])  # 燃油消耗

    # 最终将数据库转换为 DataFrame 以便更好地可视化
    database_df = pd.DataFrame(database)
    print(database_df)

    # 将 DataFrame 输出为 Excel 文件
    output_csv_path = f'{data_file}_database.csv'
    database_df.to_csv(output_csv_path, index=False)
    print(f"CSV 文件已成功保存为 {output_csv_path}")
