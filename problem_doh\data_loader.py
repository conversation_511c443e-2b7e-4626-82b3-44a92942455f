import pandas as pd
import networkx as nx
import numpy as np
import matplotlib.pyplot as plt



def load_airport_data(data_file):
    # 读取机场布局数据
    file_path = f'../data/{data_file}.txt'
    with open(file_path, 'r') as file:
        lines = file.readlines()

    # 找到节点、边和飞机数据部分
    node_section_start = lines.index('%SECTION%1%;Nodes;\n') + 1
    edge_section_start = lines.index('%SECTION%1%;Edges;\n') + 1
    aircraft_section_start = lines.index('%SECTION%1%;Aircraft;\n') + 1

    # 解析节点数据
    node_data_lines = lines[node_section_start + 1:edge_section_start - 1]
    node_data = []
    for line in node_data_lines:
        if line.strip() and not line.startswith('%'):
            parts = line.strip().split(';')
            if len(parts) >= 4:
                node_id, x, y, specification = parts[1:5]
                node_data.append([node_id, float(x), float(y), specification])

    nodes_df = pd.DataFrame(node_data, columns=['Node ID', 'X', 'Y', 'Specification'])

    # 解析边数据
    edge_data_lines = lines[edge_section_start + 1:aircraft_section_start - 1]
    edge_data = []
    for line in edge_data_lines:
        if line.strip() and not line.startswith('%'):
            parts = line.strip().split(';')
            if len(parts) >= 6:
                edge_id, start_node, end_node, _, length = parts[1:6]
                # 初始状态下，时间窗为空或可通行，None表示没有占用
                unavailable_time_windows = []
                edge_data.append([edge_id, start_node, end_node, float(length), unavailable_time_windows])

    # 创建带有时间窗的 DataFrame
    edges_df = pd.DataFrame(edge_data, columns=['Edge ID', 'Start Node', 'End Node', 'Length', 'Unavailable Time Windows'])

    # 解析飞机数据
    aircraft_data_lines = lines[aircraft_section_start + 1:]
    aircraft_data = []
    for line in aircraft_data_lines:
        if line.strip() and not line.startswith('%'):
            parts = line.replace(',', ';').strip().split(';')
            parts = parts[1:] if parts and not parts[0] else parts
            if len(parts) >= 16:
                aircraft_type = parts[1].strip().strip("'")  # 去掉单引号
                start_node = parts[2].strip().strip("'")  # 去掉单引号
                end_node = parts[3].strip().strip("'")  # 去掉单引号
                start_time_str = parts[4].strip("[]")  # 去掉时间字段的方括号
                start_time_values = start_time_str.split(';')
                start_time = float(start_time_values[0].strip())  # 取第一个时间值
                weight_class = parts[16].strip().strip("'")
                aircraft_data.append([aircraft_type, start_node, end_node, start_time, weight_class])

    aircraft_df = pd.DataFrame(aircraft_data, columns=['Type', 'Start Node', 'End Node', 'Start Time', 'Weight Class'])
    # 对aircraft_df按照Start Time进行升序排序
    aircraft_df = aircraft_df.sort_values(by='Start Time').reset_index(drop=True)

    # 创建无向图并添加节点属性
    G = nx.Graph()

    # 添加节点并设置属性
    for _, row in nodes_df.iterrows():
        G.add_node(row['Node ID'], pos=(row['X'], row['Y']), specification=row['Specification'])

    # 添加边（保持原有逻辑不变）
    for _, edge in edges_df.iterrows():
        start_node = edge['Start Node']
        end_node = edge['End Node']
        length = edge['Length']
        time_window = edge['Unavailable Time Windows']
        G.add_edge(start_node, end_node, length=length, unavailable_time_windows=time_window)
        G.add_edge(end_node, start_node, length=length, unavailable_time_windows=time_window)

    # 可视化
    plt.figure(figsize=(32, 20), dpi=150)

    # 从图属性获取位置和颜色
    pos = nx.get_node_attributes(G, 'pos')
    specs = nx.get_node_attributes(G, 'specification')
    node_colors = ['red' if specs[node] == 'runway' else
                   'green' if specs[node] == 'gate' else
                   'skyblue' for node in G.nodes()]

    nx.draw(G, pos, node_size=125, node_color=node_colors, with_labels=True,
            font_size=8, font_color='black', edge_color='gray')

    # 图例和标题保持不变
    plt.scatter([], [], c='red', label='Runway')
    plt.scatter([], [], c='green', label='Gate')
    plt.scatter([], [], c='skyblue', label='Other')
    plt.legend(loc='upper right')
    plt.title("Airport Layout Visualization")
    plt.show()

    return G, nodes_df, edges_df, aircraft_df
