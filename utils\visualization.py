import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation

class ScheduleVisualizer:
    def __init__(self, problem):
        self.problem = problem
        self.fig, self.ax = plt.subplots(figsize=(15, 10))
        
    def plot_path(self, path_info):
        """绘制路径图"""
        G = self.problem.G
        pos = nx.get_node_attributes(G, 'pos')
        edge_colors = []
        
        for edge in G.edges():
            if edge in path_info['edges']:
                edge_colors.append('red')
            else:
                edge_colors.append('lightgray')
                
        nx.draw(G, pos, edge_color=edge_colors, with_labels=True)
        plt.show()
        
    def animate_schedule(self, timelines):
        """生成调度动画"""
        fig, ax = plt.subplots()
        self.ax.set_xlim(0, 24*60)  # 24小时时间轴
        self.ax.set_ylim(0, len(self.problem.edges))
        
        # 初始化各边的位置
        edge_y = {edge: i for i, edge in enumerate(self.problem.edges)}
        
        def update(frame):
            ax.clear()
            # 绘制当前时刻各边的占用状态
            # ...具体实现...
            
        anim = FuncAnimation(fig, update, frames=np.arange(0, 24*60, 1))
        return anim 